"use client";

import React from "react";
import { LucideIcon, ChevronDown } from "lucide-react";

interface DropdownItem {
  key: string;
  label: string;
  icon: LucideIcon;
  href: string;
}

interface NavigationItemProps {
  label: string;
  icon: LucideIcon;
  isActive: boolean;
  hasDropdown?: boolean;
  dropdownItems?: DropdownItem[];
  isDropdownOpen?: boolean;
  onClick: () => void;
  onDropdownItemClick?: (itemKey: string) => void;
}

const NavigationItem: React.FC<NavigationItemProps> = ({
  label,
  icon: Icon,
  isActive,
  hasDropdown = false,
  dropdownItems = [],
  isDropdownOpen = false,
  onClick,
  onDropdownItemClick,
}) => {
  return (
    <div className="relative">
      <button
        onClick={onClick}
        className={`
          flex items-center space-x-2 px-4 py-3 font-medium whitespace-nowrap transition-all duration-200 min-w-fit
          ${
            isActive
              ? "bg-[rgb(5,148,211)] text-white"
              : "bg-white text-black hover:bg-[#61B34F] hover:text-white"
          }
        `}
      >
        <Icon className="w-4 h-4" />
        <span>{label}</span>
        {hasDropdown && (
          <ChevronDown
            className={`w-4 h-4 transition-transform duration-200 ${
              isDropdownOpen ? "rotate-180" : ""
            }`}
          />
        )}
      </button>

      {/* Dropdown Menu */}
      {hasDropdown && isDropdownOpen && (
        <div className="absolute top-full left-0 mt-0 w-48 bg-white shadow-lg border border-gray-200 py-2 z-50 max-h-80 overflow-y-auto">
          {dropdownItems.map((dropdownItem) => {
            const DropdownIcon = dropdownItem.icon;
            return (
              <button
                key={dropdownItem.key}
                onClick={() => onDropdownItemClick?.(dropdownItem.key)}
                className="w-full px-4 py-2 text-left text-gray-700 hover:bg-[rgb(5,148,211)] hover:text-white transition-colors flex items-center space-x-3 text-sm whitespace-nowrap"
              >
                <DropdownIcon className="w-4 h-4 flex-shrink-0" />
                <span className="truncate">{dropdownItem.label}</span>
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default NavigationItem;
