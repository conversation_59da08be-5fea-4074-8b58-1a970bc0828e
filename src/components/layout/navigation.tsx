"use client";

// components/Navigation.jsx
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ChevronDown,
  MapPin,
  Route,
  Anchor,
  BarChart3,
  Settings,
  AlertTriangle,
  FileText,
  TrendingUp,
  Users,
  Database,
  Menu,
} from "lucide-react";
import NavigationItem from "@/components/navigation/NavigationItem";

interface NavigationProps {
  activeTab?: string;
  onTabChange?: (key: string) => void;
  selectedReportItem?: string;
  onReportItemChange?: (key: string) => void;
}

const Navigation: React.FC<NavigationProps> = ({
  activeTab = "",
  onTabChange = () => {},
  selectedReportItem = "",
  onReportItemChange = () => {},
}) => {
  const router = useRouter();
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigationItems = [
    {
      key: "location-monitor",
      label: "Location Monitor",
      icon: MapPin,
      href: "/location-monitor",
      hasDropdown: false,
    },
    {
      key: "focused-trips",
      label: "Focused Trips",
      icon: Route,
      href: "/focused-trips",
      hasDropdown: false,
    },
    {
      key: "assigned-ports",
      label: "My Assigned Ports",
      icon: Anchor,
      href: "/assigned-ports",
      hasDropdown: false,
    },
    {
      key: "dashboard",
      label: "Dashboard",
      icon: BarChart3,
      href: "/dashboard",
      hasDropdown: false,
    },
    {
      key: "configuration",
      label: "Configuration",
      icon: Settings,
      href: "/configuration",
      hasDropdown: false,
    },
    {
      key: "suspicious-trips",
      label: "Suspicious Trips",
      icon: AlertTriangle,
      href: "/suspicious-trips",
      hasDropdown: false,
    },
    {
      key: "reports",
      label: "Reports",
      icon: FileText,
      href: "#",
      hasDropdown: true,
      dropdownItems: [
        {
          key: "trip-panel",
          label: "Trip Panel",
          icon: Route,
          href: "/reports/trip-panel",
        },
        {
          key: "all-alerts",
          label: "All Alerts",
          icon: AlertTriangle,
          href: "/reports/all-alerts",
        },
        {
          key: "alert-panel",
          label: "Alert Panel",
          icon: AlertTriangle,
          href: "/reports/alert-panel",
        },
        {
          key: "employees",
          label: "Employees",
          icon: Users,
          href: "/reports/employees",
        },
        {
          key: "assign-ports",
          label: "Assign Ports",
          icon: Anchor,
          href: "/reports/assign-ports",
        },
        {
          key: "focused-trips",
          label: "Focused Trips",
          icon: Route,
          href: "/reports/focused-trips",
        },
        {
          key: "completed-trips",
          label: "Completed Trips",
          icon: Database,
          href: "/reports/completed-trips",
        },
      ],
    },
  ];

  const handleDropdownToggle = (key: string) => {
    setOpenDropdown(openDropdown === key ? null : key);
  };

  const handleTabClick = (item: (typeof navigationItems)[number]) => {
    if (item.hasDropdown) {
      handleDropdownToggle(item.key);
    } else {
      onTabChange(item.key);
      setOpenDropdown(null);
      router.push(item.href);
    }
  };

  const handleDropdownItemClick = (parentKey: string, itemKey: string) => {
    const parentItem = navigationItems.find((item) => item.key === parentKey);
    const dropdownItem = parentItem?.dropdownItems?.find(
      (item) => item.key === itemKey
    );

    if (dropdownItem) {
      onTabChange(`${parentKey}-${itemKey}`);
      onReportItemChange(itemKey);
      setOpenDropdown(null);
      router.push(dropdownItem.href);
    }
  };

  return (
    <>
      {/* Sidebar Button for small screens */}
      {!sidebarOpen && (
        <button
          className="md:hidden absolute top-20 left-4 z-50 bg-blue-600 text-white p-2 rounded-lg shadow-lg"
          style={{ position: "absolute" }}
          onClick={() => setSidebarOpen(true)}
          aria-label="Open sidebar"
        >
          <Menu className="w-6 h-6" />
        </button>
      )}

      {/* Sidebar Drawer */}
      <div
        className={`fixed inset-0 z-40 transition-opacity duration-300 ${
          sidebarOpen
            ? "opacity-100 pointer-events-auto"
            : "opacity-0 pointer-events-none"
        }`}
        style={{ background: "transparent" }}
        onClick={() => setSidebarOpen(false)}
      >
        <nav
          className={`fixed top-0 left-0 h-full w-64 bg-[rgb(var(--nav-bg))] shadow-lg transform transition-transform duration-300 ${
            sidebarOpen ? "translate-x-0" : "-translate-x-full"
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="p-4 flex flex-col gap-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive =
                activeTab === item.key || activeTab.startsWith(`${item.key}-`);
              return (
                <button
                  key={item.key}
                  onClick={() => {
                    handleTabClick(item);
                    setSidebarOpen(false);
                  }}
                  className={`flex items-center gap-2 px-4 py-2 font-medium whitespace-nowrap transition-all duration-200 min-w-fit ${
                    isActive
                      ? "bg-[rgb(var(--nav-active))] text-[rgb(var(--nav-text-active))]"
                      : "bg-[rgb(var(--nav-bg))] text-[rgb(var(--nav-text))] hover:bg-[rgb(var(--nav-hover))] hover:text-[rgb(var(--nav-text-active))]"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.label}</span>
                </button>
              );
            })}
          </div>
        </nav>
      </div>

      {/* Main horizontal navigation (hidden on small screens) */}
      <nav className="bg-[rgb(var(--nav-bg))] relative w-full hidden md:block navigation-container">
        <div className="px-2 py-0 w-full">
          <div className="flex items-center justify-between w-full">
            {/* Navigation Items */}
            <div className="flex gap-0 navigation-container">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive =
                  activeTab === item.key ||
                  activeTab.startsWith(`${item.key}-`);

                // تحديد العنوان والأيقونة للتقارير بناءً على العنصر المختار
                let displayLabel = item.label;
                let displayIcon = Icon;

                if (item.key === "reports" && selectedReportItem) {
                  const selectedItem = item.dropdownItems?.find(
                    (dropItem) => dropItem.key === selectedReportItem
                  );
                  if (selectedItem) {
                    displayLabel = selectedItem.label;
                    displayIcon = selectedItem.icon;
                  }
                }

                return (
                  <NavigationItem
                    key={item.key}
                    label={displayLabel}
                    icon={displayIcon}
                    isActive={isActive}
                    hasDropdown={item.hasDropdown}
                    dropdownItems={item.dropdownItems || []}
                    isDropdownOpen={openDropdown === item.key}
                    onClick={() => handleTabClick(item)}
                    onDropdownItemClick={(itemKey) =>
                      handleDropdownItemClick(item.key, itemKey)
                    }
                  />
                );
              })}
            </div>

            {/* Logo in the right side */}
            <div className="flex items-center h-full">
              <img
                src="/assets/Saudi-Customs-Logo-new.png"
                alt="Saudi Customs Logo"
                className="h-20 object-cover"
              />
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navigation;
