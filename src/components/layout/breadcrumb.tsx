"use client";
// components/Breadcrumb.jsx
import React from "react";
import { Home, ChevronRight, LucideIcon, MapPin } from "lucide-react";

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: LucideIcon;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  onNavigate?: (href: string) => void;
}

// Simple logic - always Location Monitor

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items = [], onNavigate }) => {
  // Simple logic - always Location Monitor
  const defaultItems: BreadcrumbItem[] = [
    {
      label: "Home",
      href: "/",
      icon: Home,
    },
    {
      label: "Location Monitor",
      icon: MapPin,
    },
  ];

  const finalItems = items.length > 0 ? items : defaultItems;

  return (
    <div className="bg-gradient-to-r from-teal-500 to-emerald-500 text-white px-4 py-3 shadow-sm">
      <div className="flex items-center space-x-2 text-sm">
        {finalItems.map((item, index) => {
          const ItemIcon = item.icon;
          return (
            <React.Fragment key={item.href || item.label}>
              {/* عرض الأيقونة لكل عنصر */}
              {ItemIcon && <ItemIcon className="w-4 h-4 mr-1" />}
              {item.href ? (
                <button
                  onClick={() =>
                    onNavigate && item.href && onNavigate(item.href)
                  }
                  className="hover:text-teal-100 transition-colors font-medium"
                >
                  {item.label}
                </button>
              ) : (
                <span
                  className={`${
                    index === finalItems.length - 1 ? "font-medium" : ""
                  }`}
                >
                  {item.label}
                </span>
              )}
              {index < finalItems.length - 1 && (
                <ChevronRight className="w-4 h-4 opacity-70 mx-2" />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default Breadcrumb;
