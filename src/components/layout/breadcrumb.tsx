"use client";
// components/Breadcrumb.jsx
import React, { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import {
  Home,
  ChevronRight,
  LucideIcon,
  Route,
  AlertTriangle,
  Users,
  Anchor,
  Database,
  FileText,
  MapPin,
  BarChart3,
  Settings,
} from "lucide-react";

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: LucideIcon;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  onNavigate?: (href: string) => void;
}

// تعريف معلومات الصفحات
const pageInfo: Record<string, { title: string; icon: LucideIcon }> = {
  "/": { title: "Location Monitor", icon: MapPin }, // الصفحة الرئيسية
  "/location-monitor": { title: "Location Monitor", icon: MapPin },
  "/focused-trips": { title: "Focused Trips", icon: Route },
  "/assigned-ports": { title: "My Assigned Ports", icon: Anchor },
  "/dashboard": { title: "Dashboard", icon: BarChart3 },
  "/configuration": { title: "Configuration", icon: Settings },
  "/suspicious-trips": { title: "Suspicious Trips", icon: AlertTriangle },
};

// تعريف معلومات التقارير
const reportItems: Record<string, { title: string; icon: LucideIcon }> = {
  "trip-panel": { title: "Trip Panel", icon: Route },
  "all-alerts": { title: "All Alerts", icon: AlertTriangle },
  "alert-panel": { title: "Alert Panel", icon: AlertTriangle },
  employees: { title: "Employees", icon: Users },
  "assign-ports": { title: "Assign Ports", icon: Anchor },
  "focused-trips": { title: "Focused Trips", icon: Route },
  "completed-trips": { title: "Completed Trips", icon: Database },
};

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items = [], onNavigate }) => {
  const pathname = usePathname();
  const [currentPageInfo, setCurrentPageInfo] = useState<{
    title: string;
    icon: LucideIcon;
  } | null>(null);

  // إضافة state لإجبار إعادة التحديث
  const [forceUpdate, setForceUpdate] = useState(0);

  useEffect(() => {
    const updatePageInfo = () => {
      // التحقق من المسار الحالي
      if (pageInfo[pathname]) {
        setCurrentPageInfo(pageInfo[pathname]);
      } else if (pathname.startsWith("/reports/")) {
        // إذا كان في صفحة تقرير، تحقق من localStorage
        const selectedReportItem = localStorage.getItem("selectedReportItem");

        if (selectedReportItem && reportItems[selectedReportItem]) {
          setCurrentPageInfo(reportItems[selectedReportItem]);
        } else {
          // استخدم المسار لتحديد نوع التقرير
          const reportKey = pathname.split("/").pop();

          if (reportKey && reportItems[reportKey]) {
            setCurrentPageInfo(reportItems[reportKey]);
            // حفظ في localStorage للمرات القادمة
            localStorage.setItem("selectedReportItem", reportKey);
          } else {
            setCurrentPageInfo({ title: "Reports", icon: FileText });
          }
        }
      } else {
        setCurrentPageInfo({ title: "Dashboard", icon: Home });
      }
    };

    // تحديث فوري
    updatePageInfo();

    // إضافة listener لتغييرات localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "selectedReportItem") {
        updatePageInfo();
        setForceUpdate((prev) => prev + 1);
      }
    };

    // إضافة listener مخصص لتغييرات localStorage من نفس النافذة
    const handleLocalStorageChange = () => {
      updatePageInfo();
      setForceUpdate((prev) => prev + 1);
    };

    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("localStorageChange", handleLocalStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener(
        "localStorageChange",
        handleLocalStorageChange
      );
    };
  }, [pathname, forceUpdate]);

  // إنشاء breadcrumb items ديناميكياً
  const dynamicItems: BreadcrumbItem[] = [];

  if (currentPageInfo) {
    dynamicItems.push({
      label: "Home",
      href: "/",
      icon: Home,
    });

    if (pathname.startsWith("/reports/")) {
      dynamicItems.push({
        label: "Reports",
        href: "#",
        icon: FileText,
      });
    }

    dynamicItems.push({
      label: currentPageInfo.title,
      icon: currentPageInfo.icon,
    });
  }

  const finalItems = items.length > 0 ? items : dynamicItems;

  return (
    <div className="bg-gradient-to-r from-teal-500 to-emerald-500 text-white px-4 py-3 shadow-sm">
      <div className="flex items-center space-x-2 text-sm">
        {finalItems.map((item, index) => {
          const ItemIcon = item.icon;
          return (
            <React.Fragment key={item.href || item.label}>
              {/* عرض الأيقونة لكل عنصر */}
              {ItemIcon && <ItemIcon className="w-4 h-4 mr-1" />}
              {item.href ? (
                <button
                  onClick={() =>
                    onNavigate && item.href && onNavigate(item.href)
                  }
                  className="hover:text-teal-100 transition-colors font-medium"
                >
                  {item.label}
                </button>
              ) : (
                <span
                  className={`${
                    index === finalItems.length - 1 ? "font-medium" : ""
                  }`}
                >
                  {item.label}
                </span>
              )}
              {index < finalItems.length - 1 && (
                <ChevronRight className="w-4 h-4 opacity-70 mx-2" />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default Breadcrumb;
