"use client";

import React, { useEffect, useState } from "react";
import { 
  LucideIcon, 
  Route, 
  AlertTriangle, 
  Users, 
  Anchor, 
  Database,
  FileText 
} from "lucide-react";

interface ReportInfo {
  title: string;
  icon: LucideIcon;
  description: string;
}

const reportItems: Record<string, ReportInfo> = {
  "trip-panel": {
    title: "Trip Panel",
    icon: Route,
    description: "لوحة إدارة الرحلات ومتابعة حالة الشحنات"
  },
  "all-alerts": {
    title: "All Alerts",
    icon: AlertTriangle,
    description: "جميع التنبيهات والإشعارات في النظام"
  },
  "alert-panel": {
    title: "Alert Panel",
    icon: AlertTriangle,
    description: "لوحة إدارة التنبيهات والتحكم في الإشعارات"
  },
  "employees": {
    title: "Employees",
    icon: Users,
    description: "إدارة الموظفين والمستخدمين في النظام"
  },
  "assign-ports": {
    title: "Assign Ports",
    icon: Anchor,
    description: "تخصيص الموانئ للمستخدمين والموظفين"
  },
  "focused-trips": {
    title: "Focused Trips",
    icon: Route,
    description: "تقارير الرحلات المركزة والمتابعة الخاصة"
  },
  "completed-trips": {
    title: "Completed Trips",
    icon: Database,
    description: "تقارير الرحلات المكتملة والمنجزة"
  }
};

interface ReportUnderConstructionProps {
  reportKey: string;
  defaultTitle?: string;
  defaultIcon?: LucideIcon;
  defaultDescription?: string;
}

const ReportUnderConstruction: React.FC<ReportUnderConstructionProps> = ({
  reportKey,
  defaultTitle = "Reports",
  defaultIcon = FileText,
  defaultDescription = "تقارير النظام"
}) => {
  const [reportInfo, setReportInfo] = useState<ReportInfo>({
    title: defaultTitle,
    icon: defaultIcon,
    description: defaultDescription
  });

  useEffect(() => {
    // التحقق من localStorage للحصول على العنصر المختار
    const selectedReportItem = localStorage.getItem('selectedReportItem');
    
    if (selectedReportItem && reportItems[selectedReportItem]) {
      setReportInfo(reportItems[selectedReportItem]);
    } else if (reportItems[reportKey]) {
      setReportInfo(reportItems[reportKey]);
    }
  }, [reportKey]);

  const Icon = reportInfo.icon;

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
      <div className="text-center max-w-md">
        {/* Icon */}
        <div className="mb-6">
          <div className="w-24 h-24 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
            <Icon className="w-12 h-12 text-blue-600" />
          </div>
        </div>

        {/* Title */}
        <h1 className="text-3xl font-bold text-gray-800 mb-4">{reportInfo.title}</h1>

        {/* Description */}
        <p className="text-gray-600 mb-8 leading-relaxed">{reportInfo.description}</p>

        {/* Under Construction Message */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-center">
            <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center mr-3">
              <span className="text-yellow-800 text-sm font-bold">!</span>
            </div>
            <p className="text-yellow-800 font-medium">صفحة قيد الإنشاء</p>
          </div>
        </div>

        {/* Additional Info */}
        <p className="text-sm text-gray-500">
          نعمل على تطوير هذه الصفحة لتوفير أفضل تجربة لك
        </p>
      </div>
    </div>
  );
};

export default ReportUnderConstruction;
