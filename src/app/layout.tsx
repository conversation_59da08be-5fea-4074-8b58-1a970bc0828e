"use client";

import { Cairo, <PERSON>o } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/header";
import Navigation from "@/components/layout/navigation";
import Breadcrumb from "@/components/layout/breadcrumb";
import MainContent from "@/components/main-content";
import Footer from "@/components/layout/footer";
import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";

const roboto = Roboto({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  preload: true,
});

const cairo = Cairo({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  preload: true,
});

// Function to determine initial active tab from URL
function getInitialActiveTab(): string {
  if (typeof window === "undefined") return "location-monitor";

  const pathname = window.location.pathname;

  if (pathname === "/" || pathname === "/location-monitor") {
    return "location-monitor";
  } else if (pathname === "/focused-trips") {
    return "focused-trips";
  } else if (pathname === "/assigned-ports") {
    return "assigned-ports";
  } else if (pathname === "/dashboard") {
    return "dashboard";
  } else if (pathname === "/configuration") {
    return "configuration";
  } else if (pathname === "/suspicious-trips") {
    return "suspicious-trips";
  } else if (pathname.startsWith("/reports/")) {
    return "reports";
  }
  return "location-monitor";
}

export default function RootLayout() {
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState<string>(getInitialActiveTab);
  const [selectedReportItem, setSelectedReportItem] = useState<string>("");

  // تحديث selectedReportItem عند تغيير المسار للتقارير
  useEffect(() => {
    if (pathname.startsWith("/reports/")) {
      const reportKey = pathname.split("/").pop();
      if (reportKey) {
        setSelectedReportItem(reportKey);
      }
    }
  }, [pathname]);

  // Navigation handler for breadcrumb links
  const handleNavigate = (href: string) => {
    // Use tab name from link
    if (href === "/") setActiveTab("dashboard");
    else if (href === "/dashboard") setActiveTab("dashboard");
    else if (href.toLowerCase().includes("location"))
      setActiveTab("location-monitor");
    else if (href.toLowerCase().includes("focused"))
      setActiveTab("focused-trips");
    else if (href.toLowerCase().includes("assigned"))
      setActiveTab("assigned-ports");
    // Add more as needed
  };

  return (
    <html lang="en">
      <body className={roboto.className}>
        <div className="flex flex-col min-h-screen">
          <Header />
          <Navigation
            activeTab={activeTab}
            onTabChange={setActiveTab}
            selectedReportItem={selectedReportItem}
            onReportItemChange={setSelectedReportItem}
          />
          <Breadcrumb onNavigate={handleNavigate} />
          <main className="flex-1">
            <MainContent activeTab={activeTab} />
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
